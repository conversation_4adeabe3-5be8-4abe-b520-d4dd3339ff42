package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.RetryListener;
import org.springframework.stereotype.Component;

@Component
public class MonitoringR<PERSON>ryL<PERSON>ener implements RetryListener {
  private final LoggingService loggingService;

  public MonitoringRetryListener(LoggingService loggingService) {
    this.loggingService = loggingService;
  }

  @Override
  public <T, E extends Throwable> boolean open(RetryContext context, RetryCallback<T, E> callback) {
    return true;
  }

  @Override
  public <T, E extends Throwable> void close(
      RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {}

  @Override
  public <T, E extends Throwable> void onError(
      RetryContext context, RetryCallback<T, E> callback, Throwable throwable) {
    loggingService.info(
        "Retry attempt {} failed: {}", context.getRetryCount(), throwable.getMessage());
    loggingService.info("restart bc monitoring");
  }
}
