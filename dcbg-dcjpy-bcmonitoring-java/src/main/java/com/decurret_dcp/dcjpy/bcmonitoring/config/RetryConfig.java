package com.decurret_dcp.dcjpy.bcmonitoring.config;

import java.util.Collections;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

@Configuration
public class RetryConfig {
  private final MonitoringRetryListener retryListener;

  public RetryConfig(MonitoringRetryListener retryListener) {
    this.retryListener = retryListener;
  }

  @Bean
  public RetryTemplate retryTemplate() {
    RetryTemplate retryTemplate = new RetryTemplate();

    SimpleRetryPolicy retryPolicy =
        new SimpleRetryPolicy(
            5, Collections.singletonMap(Web3jConfig.Web3jConnectionException.class, true));

    FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
    backOffPolicy.setBackOffPeriod(3000);

    retryTemplate.setRetryPolicy(retryPolicy);
    retryTemplate.setBackOffPolicy(backOffPolicy);
    retryTemplate.registerListener(retryListener);

    return retryTemplate;
  }
}
