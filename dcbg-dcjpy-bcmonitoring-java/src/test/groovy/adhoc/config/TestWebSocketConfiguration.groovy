package adhoc.config

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.web3j.protocol.Web3j
import spock.mock.DetachedMockFactory

import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.BlockingQueue
import java.util.concurrent.atomic.AtomicInteger

@TestConfiguration
@Profile("test")
class TestWebSocketConfiguration {

    private final DetachedMockFactory mockFactory = new DetachedMockFactory()

    @Bean
    @Primary
    Web3j mockWeb3j() {
        def mockWeb3j = mockFactory.Mock(Web3j)
        return mockWeb3j
    }

    @Bean
    @Primary
    Web3jConfig mockWeb3jConfig() {
        def mockConfig = mockFactory.Mock(Web3jConfig)
        def mockWeb3j = mockWeb3j()

        return mockConfig
    }

    @Bean
    @Primary
    EventLogRepository mockEventLogRepository() {
        def mockRepository = mockFactory.Mock(EventLogRepository)
        return mockRepository
    }
}

@TestConfiguration
@Profile("test-websocket-failure")
class TestWebSocketFailureConfiguration {

    private final DetachedMockFactory mockFactory = new DetachedMockFactory()
    private final AtomicInteger attemptCount = new AtomicInteger(0)

    @Bean
    @Primary
    Web3jConfig mockFailingWeb3jConfig() {
        def mockConfig = mockFactory.Mock(Web3jConfig)
        return mockConfig
    }

    @Bean
    @Primary
    EventLogRepository mockEventLogRepository() {
        def mockRepository = mockFactory.Mock(EventLogRepository)
        return mockRepository
    }
}
